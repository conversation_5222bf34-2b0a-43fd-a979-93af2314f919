import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { UnviredCordovaSDK, LoginParameters, LoginType, LoginListenerType, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AlertController, MenuController, Platform } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';


@Component({
  selector: 'app-setup',
  templateUrl: './setup.page.html',
  styleUrls: ['./setup.page.scss'],
})
export class SetupPage implements OnInit {

  permissions: any;
  message: string = ""
  requiredPermissions:any[]=[]
  devicePlatform: any;
  constructor(private androidPermissions: AndroidPermissions,
    public dataService: DataService,
    public translate: TranslateService,
    // private splashScreen: SplashScreen,
    public alertService: AlertService,
    private router: Router,
    public alertController: AlertController,
    private unviredCordovaSdk: UnviredCordovaSDK,
    private menu:MenuController,
    private device:Device,
    private platForm:Platform) { }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.menu.enable(false,'menu');
    this.showPermissionsAlert()
  }

  ionViewWillLeave() {
    this.menu.enable(true,'menu');
  }

  async showPermissionsAlert() {
    this.permissions = [];
    if(parseInt(this.device.version) > 12){
      let storageReadMediaImages = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES");
      if (storageReadMediaImages.hasPermission == false ) {
        this.permissions.push("Storage");
      }
      let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS");
      if(pushNotif.hasPermission == false){
        this.permissions.push("Notifications")
      }
    } else {
      var storageRead = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
      var storadeWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
      if (storageRead.hasPermission == false || storadeWrite.hasPermission == false) {
        this.permissions.push("Storage")
      }
    }
    
    var phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
    if (phoneState.hasPermission == false) {
      this.permissions.push("Phone State")
    }
    var cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA)
    if (cameraPermission.hasPermission == false) {
      this.permissions.push("Camera")
    }
    var fineLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION)
    var coarseLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION)
    if (coarseLocation.hasPermission == false || fineLocation.hasPermission == false) {
      this.permissions.push("Location")
    }

    this.message = "App requires the following permissions please allow to continue. <br><br> "
    for (var i = 0; i < this.permissions.length; i++) {
      this.message = this.message + (i + 1) + ". " + this.permissions[i] + "<br>"
    }
    this.message = this.message + "Allow the permissions when prompted. If the the permissions are not prompted enable permissions from settings. <br> Settings > Apps > select Inspections app > Permissions > Enable all permissions  and tap Allow to continue."
    
    if (this.permissions.length > 0) {
      // await permissionAlert.present();
      // this.showPermissionsAlert();
    } else {
      this.login()
    }
  }

  login() {

    console.log("(Login:login)Started Logging in...")
    // let content = "Authenticating..."
    // this.translate.get(content).subscribe(msg => {
    //   content = msg
    // })
    var loginParameters = new LoginParameters()
    loginParameters.autoSyncTime = '10'
    loginParameters.appName = "ROPE_INSPECTIONS"
    loginParameters.company = "SAMSON"
    loginParameters.domain = "SAMSON";
    loginParameters.loginType = LoginType.unvired
    loginParameters.isRequiredAttachmentBase64 = true
    loginParameters.metadataPath = './assets/metadata.json';
    loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
    this.alertService.present().then(() => {
      this.unviredCordovaSdk.login(loginParameters).then(async (result) => {
        console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
        if (result.type == LoginListenerType.login_success) {
          var tempUser = await this.unviredCordovaSdk.userSettings();
          var tempzSettings = tempUser.data["UNVIRED_ID"];
          if(tempzSettings.substring(tempzSettings.lastIndexOf('.')+1).toLowerCase() == 'customer') {
            if(this.alertService.isLoading) {
              this.alertService.dismiss();
            }
            this.router.navigate(['home']);
          } else {
            this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "").then(async (result) => {
              if (result.type == ResultType.success) {
                if (result.data.length > 0) {
                  if(this.alertService.isLoading) {
                    this.alertService.dismiss();
                  }
                  this.router.navigate(['home']);
                } else {
                  if(this.alertService.isLoading) {
                    this.alertService.dismiss();
                  }
                  this.router.navigate(['account-setup']);
                }
              }
            }).catch(err=>{
              console.log(err);
            })
          }
        } else {
          if(this.alertService.isLoading) {
            this.alertService.dismiss();
          }
          this.router.navigate(['agreement']);
        }
      }, error => {
        console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
      })
    });
  }

  async checkHasPermissions() {
    let reqPermissions;
    if(parseInt(this.device.version) > 12) {
      reqPermissions = await this.androidPermissions.requestPermissions([
        "android.permission.READ_MEDIA_IMAGES",
        "android.permission.POST_NOTIFICATIONS",
        this.androidPermissions.PERMISSION.READ_PHONE_STATE,
        this.androidPermissions.PERMISSION.CAMERA,
        this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
        this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
      ])
    } else {
      reqPermissions = await this.androidPermissions.requestPermissions([
        this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE,
        this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE,
        this.androidPermissions.PERMISSION.READ_PHONE_STATE,
        this.androidPermissions.PERMISSION.CAMERA,
        this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
        this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
      ])
    }
    
    if (reqPermissions.hasPermission == false) {
      this.showPermissionsAlert();
    } else {
      this.login();
    }
  }

  exitApp() {
    navigator['app'].exitApp();
  }

}
