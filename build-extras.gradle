 android {
 configurations {

        // Only exclude listenablefuture to avoid conflicts
        all*.exclude group: 'com.google.guava', module: 'listenablefuture'
        // Don't exclude entire guava library - Firebase needs ImmutableSet
        // all*.exclude group: 'com.google.guava', module: 'guava'
        // Don't exclude OkHttp - Firebase Functions needs it
        // all*.exclude group: 'com.squareup.okhttp3', module: 'okhttp'

    }
 }

 dependencies {
    constraints {
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0") {
            because("kotlin-stdlib-jdk7 is now a part of kotlin-stdlib")
        }
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0") {
            because("kotlin-stdlib-jdk8 is now a part of kotlin-stdlib")
        }
        // Force consistent OkHttp version for Firebase compatibility
        implementation("com.squareup.okhttp3:okhttp:4.12.0") {
            because("Firebase Functions requires OkHttp")
        }
        implementation("com.squareup.okhttp3:logging-interceptor:4.12.0") {
            because("Consistent OkHttp version")
        }
    }
}