import { Component, HostListener, NgZone, OnInit } from '@angular/core';

import { Platform, AlertController, ToastController, NavController } from '@ionic/angular';
// import { SplashScreen } from '@ionic-native/splash-screen/ngx';
// import { StatusBar } from '@ionic-native/status-bar/ngx';
import {
  faTachometerAlt, faFile, faPlusCircle, faHistory, faPlusSquare, faServer, faEnvelope, faTh, faInfo, faInfoCircle, faShip, faBookOpen, faChartBar, faFileAlt, faHouse, faListCheck,
  faGrip
} from '@fortawesome/free-solid-svg-icons';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from './services/help.service';
import { UnviredCordovaSDK, LoginParameters, LoginType, LoginListenerType, LogLevel, LoginResult, ResultType, AuthenticateAndActivateResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstant } from '../constants/appConstants';
import { AlertService } from './services/alert.service';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { DataService, } from './services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import { UserPreferenceService } from './services/user-preference.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { PlatformService } from './services/platform.service';

declare var measurement: any;

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {

  appVersion: string = "99.99.99";
  platformId: string = this.platformService.getPlatformId();
  permissions: any;
  token: string = '';
  public appPages = [
    {
      title: this.translate.instant('Home'),
      url: '/home',
      icon: 'house'
    },
    {
      title: this.translate.instant('Inspection'),
      url: '/detailed-routine-inspection',
      src: './assets/icon/Rope_Inspection_ICON_3A.png'
      // icon: 'search'
    },
    {
      title: this.translate.instant("Insight-quick-inspect"),
      url: '/insight-AI-home',
      src: './assets/icon/Icaria_Insight_AI.png'
    },
    {
      title: this.translate.instant('Line Management'),
      url: '/line-tracker-home',
      icon: 'list-check'
    },
    {
      title: this.translate.instant('Resources'),
      url: '/resource',
      icon: 'grip'
    },
    {
      title: this.translate.instant('Documents'),
      url: '/Documents',
      // icon: 'file-alt'
      src: './assets/icon/document.png'
    },
    {
      title: this.translate.instant('Fleets'),
      url: '/Fleets',
      // icon: 'ship'
      src: './assets/icon/boat.png'
    },
    {
      title: this.translate.instant('Classroom'),
      url: '/Classroom',
      // icon: 'book-open'
      src: './assets/icon/classroom.png'
    },
    {
      title: this.translate.instant('Dashboard'),
      url: '/Dashboard',
      // icon: 'chart-bar'
      src: './assets/icon/dashboard.png'
    },
    // {
    //   title: 'Amsteel Blue',
    //   url: '/product-type',
    //   icon: 'file'
    // },
    {
      title: 'Abrasion Comparator',
      url: '/abrasion-comparator',
      icon: 'grip'
    },
    {
      title: this.translate.instant('Contact'),
      url: '/contact',
      icon: 'envelope'
    }
  ];
  selectedUser: any;
  selectedIndustry: any;

  constructor(
    private platform: Platform,
    // private splashScreen: SplashScreen,
    private router: Router,
    public helpService: HelpService,
    private translate: TranslateService,
    public alertService: AlertService,
    public dataService: DataService,
    public alertController: AlertController,
    private androidPermissions: AndroidPermissions,
    private unviredCordovaSdk: UnviredCordovaSDK,
    public toastController: ToastController,
    public ngZone: NgZone,
    public navCtrl: NavController,
    public device: Device,
    public userPreferenceService: UserPreferenceService,
    private screenOrientation: ScreenOrientation,
    private faIconLibrary: FaIconLibrary,
    public platformService: PlatformService
  ) { 
    this.faIconLibrary.addIcons(faTachometerAlt, faFileAlt, faPlusCircle, faHistory, faServer, faPlusSquare, faHouse, faListCheck, faEnvelope, faGrip, faInfo, faInfoCircle, faShip, faBookOpen, faChartBar, faFile);
  }

  ngOnInit() {
    this.translate.setDefaultLang('en');
    this.translate.use('en');
    this.alertService.setAppConstItem(this)
    this.platform.ready().then(async () => {
      this.unviredCordovaSdk.logInfo("APP-COMPONENT", "ngOnInit", "Platform is ready, App initialization started")
      await this.initializeApp();
      this.unviredCordovaSdk.logInfo("APP-COMPONENT", "ngOnInit", "App initialization completed")
      console.log("app initialization is completed");
      this.setPortrait();
      this.unviredCordovaSdk.logInfo("APP-COMPONENT", "ngOnInit", "Check User enabled for Insight AI started")
      await this.checkInsightAIEnabledForUser(); // * check whether is enabled for Insight AI
      this.unviredCordovaSdk.logInfo("APP-COMPONENT", "ngOnInit", "Check User enabled for Insight AI completed")
      await this.setImageUploadSizeLimit()
    });
  }

  async setImageUploadSizeLimit() {
    let result = await this.unviredCordovaSdk.dbSelect(AppConstant.TABLE_USER_HEADER,'');
    if(result.type == ResultType.success) {
      if(result.data.length > 0 && result.data[0].EXTFLD3!=null && result.data[0].EXTFLD3 != undefined && result.data[0].EXTFLD3!='')  {
        let imageSize;
        imageSize = parseFloat(result.data[0].EXTFLD3);
        if(!isNaN(imageSize)) {
          console.log("Measurement images upload Size Limit: " + imageSize);
          AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB = imageSize;
          console.log(AppConstant.MAX_ALLOWED_IMAGE_SIZE_MB);
        }
      }
    }
  }

  async checkInsightAIEnabledForUser() {
    try {
      let insightAIEnabled = await this.dataService.userEnabledForInsightAI();
    } catch (error) {
      await this.unviredCordovaSdk.logInfo("APP-COMPONENT",'checkInsightAIEnabledForUser',"Error checking Insight AI enabled for user: " + JSON.stringify(error));
    }
    // if(insightAIEnabled) {
    //   // console.log(insightAIEnabled);
    // }
  }

  initializeApp() {
    window.addEventListener('keyboardWillShow', (event) => {
      document.activeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      setTimeout(() => {
        window.scrollBy(0, -500);
      }, 1000)
    });
    window.addEventListener('keyboardDidShow', (event) => {
      document.activeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      setTimeout(() => {
        window.scrollBy(0, -500);
      }, 1000)
    });

    window.addEventListener('offline', () => {
      this.dataService.setNetworkStatus(false)
    });

    window.addEventListener('online', () => {
      this.dataService.setNetworkStatus(true)
    });
    window.addEventListener('beforeunload', (e) => {
      if (this.token != '') {
        var confirmationMessage = 'It looks like you have been editing something. '
          + 'If you leave before saving, your changes will be lost.';

        (e || window.event).returnValue = confirmationMessage; //Gecko + IE
        return confirmationMessage; //Gecko + Webkit, Safari, Chrome etc.
      }
    });

    this.getAppVersion();
    // if (this.platform.is("ios") || this.platform.is("ipad") || this.platform.is("iphone") || this.device.platform == 'windows') {
    //   // this.statusBar.styleDefault();
    // }

    this.permissions = ["Storage", "Phone State", "Camera", "Location"]
    // this.router.navigate(['home']);

    if (this.platform.is("android")) {
      // this.checkArAndPreasentToast()
      this.showPermissionsAlert();
    } else if (this.device.platform == "browser") {
      this.dataService.selectedRole = "Customer"
      var redirectLink = location.href.substring(location.href.indexOf('?') + 1)
      if (redirectLink.includes('data=')) {

        var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
        if (tempToken == '' || tempToken == null) {
          this.dataService.browserFirstLogIn = true;
        }

        // console.log(redirectLink)
        // console.log(redirectLink.substring(redirectLink.indexOf('?data=') + 6))
        // var decodedData = redirectLink.substring(redirectLink.indexOf('?data=') + 6)
        decodedData = decodeURIComponent(decodedData)
        localStorage.setItem("data", decodedData)
        var decodedString = atob(decodedData);
        // console.log(decodedString);
        var decodedJson = JSON.parse(decodedString);
        // this.loginWithUser(decodedJson.UserName, decodedJson.Password);

        // var redirectLink = location.href.substring(location.href.indexOf('?') + 1)
        if (decodedJson != undefined && decodedJson != null && decodedJson != '') {
          if (decodedJson.redirect != '' && decodedJson.redirect != undefined) {
            this.dataService.redirectPage = decodedJson.redirect
          }
        }

        if (decodedJson != undefined && decodedJson != null && decodedJson != '') {
          if (decodedJson.token != '' && decodedJson.token != undefined) {
            this.token = decodedJson.token
            localStorage.setItem("ROPE_INSPECTIONS_token", this.token)
            // if(this.dataService.browserFirstLogIn == true) {
            //   var obj = {"appName":"ROPE_INSPECTIONS","url":AppConstant.URL,"username":decodedJson.UserName}
            //   localStorage.setItem("ROPE_INSPECTIONS", JSON.stringify(obj));
            // }
            this.loginBrowser(decodedJson);
          } else {
            var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
            if (tempToken == '' || tempToken == null) {
              this.router.navigate(['browser-home']);
              // this.splashScreen.hide();
              this.alertService.dismiss();
            } else {
              this.loginBrowser(decodedJson);
            }
          }
        }
      } else if (redirectLink.includes('error=')) {
        var decodedData = redirectLink.substring(redirectLink.indexOf('?error=') + 7)
        decodedData = decodeURIComponent(decodedData)
        console.log(decodedData);
        this.alertService.showAlert("Error", decodedData)
        this.router.navigate(['browser-home']);
        // this.splashScreen.hide();
        this.alertService.dismiss();
      } else {
        var tempToken = localStorage.getItem("ROPE_INSPECTIONS_token")
        if (tempToken == '' || tempToken == null) {
          this.router.navigate(['browser-home']);
          // this.splashScreen.hide();
          this.alertService.dismiss();
        } else {
          this.loginBrowser(decodedJson);
        }
      }
      // var redirectLink = location.href.substring(location.href.indexOf('?') + 1)
      // if(redirectLink.includes('redirect')) {
      //   this.dataService.redirectPage = redirectLink.substring(redirectLink.indexOf('redirect=') + 9)
      //   if(this.dataService.redirectPage.includes('&')) {
      //     this.dataService.redirectPage = this.dataService.redirectPage.substr(0, this.dataService.redirectPage.indexOf('&'))
      //   }
      // }
      // if(redirectLink.includes('token')) {
      //  this.token = redirectLink.substring(redirectLink.indexOf('token=') + 6)
      //   if(this.dataService.redirectPage.includes('&')) {
      //     this.token = this.dataService.redirectPage.substr(0, this.dataService.redirectPage.indexOf('&'))
      //   }
      //   localStorage.setItem("ROPE_INSPECTIONS_token", this.token)
      //   localStorage.setItem("ROPE_INSPECTIONS", '{"appName":"ROPE_INSPECTIONS","url":"https://sandbox.unvired.io/UMP","username":"<EMAIL>"}')
      //   this.login();
      // } else {
      //   this.router.navigate(['browser-home']);
      //   this.splashScreen.hide();
      //   this.alertService.dismiss();
      // }

    } else {
      this.login();
    }

    if (this.platformId !== 'electron') {
      this.platform.backButton.subscribeWithPriority(9999, () => {
        var url = this.router.url
        if (this.router.url === '/home' || this.router.url === '/login' || this.router.url === '/agreement' || this.router.url === '/account-setup') {
          navigator['app'].exitApp();
        } else {
          //Do nothing.
        }
      });
    } else if (this.platformId == 'electron') {
      // Hide Windows top-left UWP bcak button
      console.log('Platform identified is: ', this.device.platform);
      setTimeout(() => {
        this.hideWindowsUWPBackButton();
      }, 12 * 100);
    }
  }

  bindEvents() {
    document.addEventListener('deviceready', this.onDeviceReady, false);
    document.addEventListener('pause', this.onPause, false);
    document.addEventListener('resume', this.onResume, false);
  }

  onDeviceReady() {
    alert("DeviceReady")
  }
  onPause() {
    alert('pausing 2')
  }
  onResume(event) {
    alert("resuming 2 " + JSON.stringify(event))
  }

  hideWindowsUWPBackButton() {
    try {
      var win: any = window;
      var currentView = win.Windows.UI.Core.SystemNavigationManager.getForCurrentView();
      console.log('App view back button visibility current view: ', currentView);
      currentView.appViewBackButtonVisibility = win.Windows.UI.Core.AppViewBackButtonVisibility.collapsed;
    } catch (error) {
      console.error(`Error in hideWindowsTitleBackArrow: ${error}`);
    }
  }


  getEdgeStart() {
    return window.innerWidth;
  }

  loginBrowser(decodedJson) {
    // this.dataService.loadAppConfig()
    var data = {
      "apiBaseUrl": AppConstant.URL
    }
    fetch('./assets/config.json').then(res => res.json())
      .then(json => {
        data = json;
        console.log("(Login:login)Started Logging in...")
        var loginParameters = new LoginParameters()
        loginParameters.autoSyncTime = '10'
        loginParameters.appName = "ROPE_INSPECTIONS"
        loginParameters.company = "SAMSON"
        loginParameters.domain = "SAMSON";
        loginParameters.username = decodedJson ? decodedJson.UserName : '';
        loginParameters.password = decodedJson ? decodedJson.Password : '';
        loginParameters.cacheWebData = true;
        loginParameters.loginType = LoginType.unvired
        loginParameters.isRequiredAttachmentBase64 = true
        loginParameters.demoModeRequired = true;
        loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
        loginParameters.metadataPath = './assets/metadata.json';
        loginParameters.url = data.apiBaseUrl
        this.dataService.setLoggedInUrl(data.apiBaseUrl)
        this.alertService.present().then(() => {
          // if(this.dataService.browserFirstLogIn == false) {
          this.unviredCordovaSdk.login(loginParameters).then((result) => {
            console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
            if (result.type == LoginListenerType.login_success) {
              this.unviredCordovaSdk.setLogLevel(LogLevel.important).then((result) => {
                console.log(JSON.stringify(result))
              }).catch((error) => {
                console.log(JSON.stringify(error))
              });
              this.router.navigate(['home']);
              // this.splashScreen.hide();
              this.alertService.dismiss();
            } else if (result.type == 7) {
              this.unviredCordovaSdk.setLogLevel(LogLevel.important).then((result) => {
                console.log(JSON.stringify(result))
              }).catch((error) => {
                console.log(JSON.stringify(error))
              });
              this.ngZone.run(() => {
                this.alertService.changeMenuItems()
              })
              this.router.navigate(['guest-home']);
              // this.splashScreen.hide();
              this.alertService.dismiss();
            } else {
              this.router.navigate(['browser-home']);
              // this.splashScreen.hide();
              this.alertService.dismiss();
            }
          }, error => {
            console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
          })
          // } else {

          // this.unviredCordovaSdk.authenticateAndActivate(loginParameters).then((result) => {
          //   if(result.type == AuthenticateAndActivateResultType.auth_activation_error) {
          //     this.unviredCordovaSdk.logError("LOGIN", "Login Browser","Authentication Error " + JSON.stringify(result))
          //     console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
          //     this.alertService.dismiss()
          //   } else {
          //     console.log("(LOGIN: authenticateAndActivate)Success " + JSON.stringify(result))
          //     this.getCustomization();
          //   }
          //   // this.route.navigate(['home']);
          // }, error => {
          //   console.log("(LOGIN: authenticateAndActivate)Error " + JSON.stringify(error))
          // })
          // }
        });
      }, async error => {
        const errorToast = await this.toastController.create({
          message: 'Config file not loaded.',
          position: 'bottom',
          buttons: [{
            text: 'Done',
            role: 'cancel',
            handler: () => {
              console.log('Cancel clicked');
            }
          }
          ]
        });
        errorToast.present();
      });

  }


  getCustomization() {
    // this.unviredCordovaSdk.syncBackground(RequestType.QUERY, "", "", AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, "", "", true).then((result) => {
    //   alert(JSON.stringify(result))
    // })
    this.dataService.getCustomization();
    setTimeout(() => {
      this.ngZone.run(async () => {
        this.unviredCordovaSdk.setLogLevel(LogLevel.important).then((result) => {
          console.log(JSON.stringify(result))
        }).catch((error) => {
          console.log(JSON.stringify(error))
        });
        console.log("==========" + this.dataService.selectedRole == "Customer" + "===========")

        if (this.dataService.selectedRole == "Customer") {
          this.navCtrl.navigateRoot(['home']);
        } else {
          var tempUserSetting = await this.unviredCordovaSdk.userSettings();
          if (tempUserSetting.type != ResultType.success) {
            var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '', '', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
            if (accountList.type == ResultType.success) {
              var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
                if (selectedPrevoiusAccpount == '' || selectedPrevoiusAccpount == undefined || selectedPrevoiusAccpount == null || selectedPrevoiusAccpount == 'null') {
                  await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
                  await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
                this.navCtrl.navigateRoot(['account-setup']);
                } else {
                  this.navCtrl.navigateRoot(['home']);
                }
            } else {
              this.dataService.showErrorAlert(accountList.error);
            }
          } else {
            if(tempUserSetting.data.USER_ID.toUpperCase().indexOf('CUSTOMER') > -1 ) {
              this.dataService.selectedRole = 'Customer'
              this.navCtrl.navigateRoot(['home']);
            } else {
              this.dataService.selectedRole = 'Employee'
              var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '', '', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
            if (accountList.type == ResultType.success) {
              var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
                if (selectedPrevoiusAccpount == '' || selectedPrevoiusAccpount == undefined || selectedPrevoiusAccpount == null || selectedPrevoiusAccpount == 'null') {
                  await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
                  await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
                  this.navCtrl.navigateRoot(['account-setup']);
                } else {
                  this.navCtrl.navigateRoot(['home']);
                }
            } else {
              this.dataService.showErrorAlert(accountList.error);
            }
            }
          }
        }
      })
      this.alertService.dismiss();
    }, 1000)
    // this.unviredCordovaSdk.syncForeground(RequestType.QUERY, "", "", "ROPE_INSPECTIONS_PA_GET_CUSTOMIZATION", true).then((result) => {
    // console.log("(LOGIN: getCustomization())    " + JSON.stringify(result))
    // this.route.navigate(['home']);
    // this.alertService.dismiss();
    // }, error => {
    //   console.log("(LOGIN: getCustomization()) error    " + JSON.stringify(error))
    // });
  }


  login() {

    console.log("(Login:login)Started Logging in...")
    var loginParameters = new LoginParameters()
    loginParameters.autoSyncTime = '10'
    loginParameters.appName = "ROPE_INSPECTIONS"
    loginParameters.company = "SAMSON"
    loginParameters.domain = "SAMSON";
    loginParameters.cacheWebData = true;
    loginParameters.loginType = LoginType.unvired
    loginParameters.isRequiredAttachmentBase64 = true
    loginParameters.demoModeRequired = true;
    loginParameters.metadataPath = './assets/metadata.json';
    loginParameters.jwtOptions = { app: 'inspections', language: 'en' };
    this.alertService.present().then(() => {
      this.unviredCordovaSdk.login(loginParameters).then(async (result) => {
        console.log("(LOGIN: Lognin)Success " + JSON.stringify(result))
        if (result.type == LoginListenerType.login_success) {
          this.unviredCordovaSdk.setLogLevel(LogLevel.important).then((result) => {
            console.log(JSON.stringify(result))
          }).catch((error) => {
            console.log(JSON.stringify(error))
          });
          console.log("==========" + this.dataService.selectedRole == "Customer" + "===========")
          if (this.dataService.selectedRole == "Customer") {
            this.navCtrl.navigateRoot(['home']);
          } else {
            var tempUserSetting = await this.unviredCordovaSdk.userSettings();
            if (tempUserSetting.type != ResultType.success) {
              var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '', '', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
              if (accountList.type == ResultType.success) {
                var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
                if (selectedPrevoiusAccpount == '' || selectedPrevoiusAccpount == undefined || selectedPrevoiusAccpount == null || selectedPrevoiusAccpount == 'null') {
                  await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
                  await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
                  this.navCtrl.navigateRoot(['account-setup']);
                } else {
                  this.navCtrl.navigateRoot(['home']);
                }
              } else {
                this.dataService.showErrorAlert(accountList.error);
              }
            } else {
              if(tempUserSetting.data.USER_ID != null){
                if (tempUserSetting.data.USER_ID.toUpperCase().indexOf('CUSTOMER') > -1) {
                  this.navCtrl.navigateRoot(['home']);
                  this.dataService.selectedRole = 'Customer'
                } else {
                  this.navCtrl.navigateRoot(['home']);
                  this.dataService.selectedRole = 'Employee'
                }
              }
              else {
                this.dataService.selectedRole = 'Employee'
                var accountList = await this.unviredCordovaSdk.syncForeground(RequestType.PULL, '', '', 'ROPE_INSPECTIONS_PA_GET_ACCOUNT_AND_ASSETS', true);
                if (accountList.type == ResultType.success) {
                  var selectedPrevoiusAccpount = await this.userPreferenceService.getUserPreference('account');
                  if (selectedPrevoiusAccpount == '' || selectedPrevoiusAccpount == undefined || selectedPrevoiusAccpount == null || selectedPrevoiusAccpount == 'null') {
                    await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'false');
                    await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
                    this.navCtrl.navigateRoot(['account-setup']);
                  } else {
                    this.navCtrl.navigateRoot(['home']);
                  }
                } else {
                  // this.dataService.showErrorAlert(accountList.error);
                  this.navCtrl.navigateRoot(['home']);
                }
              }
            }
          }
          // this.splashScreen.hide();
          await this.alertService.dismiss();
        } else if (result.type == 7) {
          this.unviredCordovaSdk.setLogLevel(LogLevel.important).then((result) => {
            console.log(JSON.stringify(result))
          }).catch((error) => {
            console.log(JSON.stringify(error))
          });
          this.ngZone.run(() => {
            this.alertService.changeMenuItems()
          })
          this.router.navigate(['guest-home']);
          // this.splashScreen.hide();
          await this.alertService.dismiss();
        } else {
          this.router.navigate(['agreement']);
          // this.splashScreen.hide();
          await this.alertService.dismiss();
        }
      }, error => {
        console.log("(LOGIN: Lognin)Error " + JSON.stringify(error))
      })
    });
  }

  async getAppVersion() {
    // var temp = await this.unviredCordovaSdk.getVersionNumbers()
    // console.log("app Version     ++++++++"   +JSON.stringify(temp))
    // console.log("app Version     ++++++++"   +temp.data.appVersion)
    // this.appVersion = temp.data.appVersion

    this.appVersion = AppConstant.VERSION_NO + " - " + AppConstant.BUILD_DATE
  }

  async checkHasPermissions() {
    this.permissions = [];
    var reqPermissions;
    if(parseInt(this.device.version) > 12) {
      reqPermissions = await this.androidPermissions.requestPermissions([
        "android.permission.READ_MEDIA_IMAGES",
        "android.permission.POST_NOTIFICATIONS",
        this.androidPermissions.PERMISSION.READ_PHONE_STATE,
        this.androidPermissions.PERMISSION.CAMERA,
        this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
        this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
      ])
    } else {
      reqPermissions = await this.androidPermissions.requestPermissions([
        this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE,
        this.androidPermissions.PERMISSION.MANAGE_EXTERNAL_STORAGE,
        this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE,
        this.androidPermissions.PERMISSION.READ_PHONE_STATE,
        this.androidPermissions.PERMISSION.CAMERA,
        this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION,
        this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION
      ])
    }
    if (reqPermissions.hasPermission == false) {
      if( parseInt(this.device.version) > 12) {
        let storageRead = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES")
        if (storageRead.hasPermission == false) {
          this.permissions.push("Storage")
        }
        let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS")
        if (pushNotif.hasPermission == false) {
          this.permissions.push("Notifications")
        }
      } else {
        var storageRead = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
        var storadeWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
        var manageExternalStorage = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.MANAGE_EXTERNAL_STORAGE)
        if (storageRead.hasPermission == false || storadeWrite.hasPermission == false || manageExternalStorage.hasPermission == false ) {
          this.permissions.push("Storage")
        }
      }

      var phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
      if (phoneState.hasPermission == false) {
        this.permissions.push("Phone State")
      }
      var cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA)
      if (cameraPermission.hasPermission == false) {
        this.permissions.push("Camera")
      }
      var fineLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION)
      var coarseLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION)
      if (coarseLocation.hasPermission == false || fineLocation.hasPermission == false) {
        this.permissions.push("Location")
      }
      this.showPermissionsAlert();
    } else {
      this.login();
    }
  }

  async showPermissionsAlert() {
    this.permissions = [];
    if(parseInt(this.device.version) > 12){
      let storageReadMediaImages = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES");
      if (storageReadMediaImages.hasPermission == false ) {
        this.permissions.push("Storage");
      }
      let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS");
      if (pushNotif.hasPermission == false) {
        this.permissions.push("Notifications");
      }
    } else {
      var storageRead = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE)
      var storadeWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE)
      if (storageRead.hasPermission == false || storadeWrite.hasPermission == false) {
        this.permissions.push("Storage")
      }
    }
    var phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE)
    if (phoneState.hasPermission == false) {
      this.permissions.push("Phone State");
    }
    var cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA)
    if (cameraPermission.hasPermission == false) {
      this.permissions.push("Camera")
    }
    var fineLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION)
    var coarseLocation = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION)
    if (coarseLocation.hasPermission == false || fineLocation.hasPermission == false) {
      this.permissions.push("Location")
    }
    
    if (this.permissions.length > 0) {
      // await permissionAlert.present();
      this.router.navigate(['setup']);
    } else {
      this.login();
    }
  }

  checkArAndPreasentToast() {
    measurement.isARSupported({}, enabled => {
      console.log(enabled)
      if (enabled == true) {

      } else {
        this.presentToastWithOptions();
      }
    }, error => {

    });
  }

  async presentToastWithOptions() {
    const toast = await this.toastController.create({
      message: 'AR not supported',
      position: 'top',
      buttons: [{
        text: 'Done',
        role: 'cancel',
        handler: () => {
          console.log('Cancel clicked');
        }
      }
      ]
    });
    toast.present();
  }

  openPage(p) {
    console.log(JSON.stringify(p))
    this.dataService.setConfigurationOption()
    this.dataService.setLastUsedAccount();
    this.dataService.setLastUsedAsset();
    this.dataService.setLastUsedWorkOrder();
    if (p.url == '/line-tracker-home') {
      this.dataService.navigateToLineTracker(this)
    } else if (p.url == '/home') {
      this.navCtrl.navigateBack(['/home']);
    } else if (p.url == '/guest-inspection-home') {
      this.dataService.gotoGuestInspections();
    } else if (p.url == '/Documents') {
      this.dataService.openUrlInBrowser("Documents");
    } else if (p.url == '/Fleets') {
      this.dataService.openUrlInBrowser("Fleets");
    } else if (p.url == '/Classroom') {
      this.dataService.openUrlInBrowser("Classroom");
    } else if (p.url == '/Dashboard') {
      this.dataService.openUrlInBrowser("Dashboard");
    } else {
      if (this.device.platform != "browser") {
        this.router.navigateByUrl(p.url)
      } else {
        if (p.url == '/resource') {
          // this.dataService.gotoResources()
          this.dataService.openUrlInBrowser("Resources");
        // } else if (p.url == '/contact') {
        //   this.dataService.gotoContact()
        } else {
          this.router.navigateByUrl(p.url)
        }
      }
    }
  }

  setPortrait() {
    // set to portrait
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
  }

  saveWebDb() {
    this.unviredCordovaSdk.dbSaveWebData();
  }
}