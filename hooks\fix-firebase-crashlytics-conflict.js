#!/usr/bin/env node

/**
 * Cordova Hook: Fix Firebase Crashlytics Plugin Conflicts
 *
 * This hook automatically fixes conflicts between cordova-plugin-firebase-crash
 * and cordova-plugin-firebasex by removing the duplicate Crashlytics plugin
 * application from the firebase-crash plugin.
 *
 * Updated to also ensure Guava dependencies are properly handled for Firebase services.
 *
 * The hook runs before the Android build process.
 */

const fs = require('fs');
const path = require('path');

module.exports = function(context) {
    console.log('Running Firebase Crashlytics conflict fix hook...');
    
    // Only run for Android platform
    if (context.opts.platforms.indexOf('android') === -1) {
        console.log('Skipping Firebase Crashlytics fix - not building for Android');
        return;
    }

    const platformRoot = path.join(context.opts.projectRoot, 'platforms', 'android');
    const firebaseCrashGradlePath = path.join(platformRoot, 'cordova-plugin-firebase-crash', 'inspections-build.gradle');

    function fixFirebaseCrashPlugin() {
        if (!fs.existsSync(firebaseCrashGradlePath)) {
            console.log('cordova-plugin-firebase-crash build.gradle not found - skipping fix');
            return;
        }

        const fixedContent = `// buildscript removed - no longer needed since Crashlytics is handled by firebasex plugin

// Crashlytics plugin application removed to avoid conflict with cordova-plugin-firebasex
// The firebasex plugin will handle Crashlytics configuration

android {
    buildTypes {
        debug {
            // Crashlytics configuration moved to firebasex plugin
        }
    }
}
`;

        try {
            const data = fs.readFileSync(firebaseCrashGradlePath, 'utf8');
            
            // Check if already fixed
            if (data.includes('// buildscript removed - no longer needed')) {
                console.log('✓ Firebase Crashlytics conflict already fixed');
                return;
            }

            // Check if the file contains the problematic plugin application
            if (data.includes('apply plugin: com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsPlugin')) {
                // Write the fixed content
                fs.writeFileSync(firebaseCrashGradlePath, fixedContent, 'utf8');
                console.log('✓ Fixed Firebase Crashlytics conflict by removing duplicate plugin from firebase-crash');
            } else {
                console.log('No Firebase Crashlytics plugin conflict found');
            }
        } catch (err) {
            console.error(`Error processing ${firebaseCrashGradlePath}:`, err.message);
        }
    }

    // Run the fix
    fixFirebaseCrashPlugin();
    console.log('Firebase Crashlytics conflict fix hook completed');
};
